"""
File Upload and Parsing System for Test Case Generator
Handles .txt, .docx, and Excel file parsing and content extraction
"""

import os
import shutil
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
from docx import Document
import uuid


class FileParser:
    """Handles file upload and content parsing"""
    
    def __init__(self, upload_dir: str = "data/uploads"):
        self.upload_dir = upload_dir
        self.ensure_upload_directory()
        self.supported_extensions = {'.txt', '.docx', '.xlsx', '.xls'}
    
    def ensure_upload_directory(self):
        """Ensure upload directory exists"""
        if not os.path.exists(self.upload_dir):
            os.makedirs(self.upload_dir)
    
    def is_supported_file(self, filename: str) -> bool:
        """Check if file extension is supported"""
        _, ext = os.path.splitext(filename.lower())
        return ext in self.supported_extensions
    
    def save_uploaded_file(self, file_path: str, original_name: str) -> <PERSON>ple[str, str]:
        """
        Save uploaded file to upload directory
        
        Args:
            file_path: Path to the uploaded file
            original_name: Original filename
            
        Returns:
            Tuple of (saved_file_path, file_type)
        """
        if not self.is_supported_file(original_name):
            raise ValueError(f"Unsupported file type. Supported: {', '.join(self.supported_extensions)}")
        
        # Generate unique filename to avoid conflicts
        file_id = str(uuid.uuid4())
        _, ext = os.path.splitext(original_name.lower())
        new_filename = f"{file_id}_{original_name}"
        saved_path = os.path.join(self.upload_dir, new_filename)
        
        # Copy file to upload directory
        shutil.copy2(file_path, saved_path)
        
        return saved_path, ext
    
    def parse_txt_file(self, file_path: str) -> str:
        """Parse text file and extract content"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content.strip()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
                return content.strip()
            except Exception as e:
                raise ValueError(f"Error reading text file: {str(e)}")
    
    def parse_docx_file(self, file_path: str) -> str:
        """Parse DOCX file and extract text content"""
        try:
            doc = Document(file_path)
            content_parts = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text.strip())
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content_parts.append(" | ".join(row_text))
            
            return "\n".join(content_parts)
        except Exception as e:
            raise ValueError(f"Error reading DOCX file: {str(e)}")
    
    def parse_excel_file(self, file_path: str) -> str:
        """Parse Excel file and extract content from all sheets"""
        try:
            # Read all sheets
            excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            content_parts = []
            
            for sheet_name, df in excel_data.items():
                if not df.empty:
                    content_parts.append(f"=== Sheet: {sheet_name} ===")
                    
                    # Convert DataFrame to string representation
                    # Handle NaN values
                    df_clean = df.fillna('')
                    
                    # Add column headers
                    headers = " | ".join(str(col) for col in df_clean.columns)
                    content_parts.append(headers)
                    content_parts.append("-" * len(headers))
                    
                    # Add rows
                    for _, row in df_clean.iterrows():
                        row_text = " | ".join(str(val) for val in row.values)
                        if row_text.strip():
                            content_parts.append(row_text)
                    
                    content_parts.append("")  # Add blank line between sheets
            
            return "\n".join(content_parts)
        except Exception as e:
            raise ValueError(f"Error reading Excel file: {str(e)}")
    
    def parse_file(self, file_path: str, file_type: str) -> str:
        """
        Parse file based on its type and extract content
        
        Args:
            file_path: Path to the file
            file_type: File extension (e.g., '.txt', '.docx', '.xlsx')
            
        Returns:
            Extracted text content
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_type = file_type.lower()
        
        if file_type == '.txt':
            return self.parse_txt_file(file_path)
        elif file_type == '.docx':
            return self.parse_docx_file(file_path)
        elif file_type in ['.xlsx', '.xls']:
            return self.parse_excel_file(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
    
    def extract_requirements_info(self, content: str) -> Dict[str, Any]:
        """
        Extract structured information from parsed content
        This is a basic implementation that can be enhanced based on specific needs
        """
        info = {
            'raw_content': content,
            'functions': [],
            'requirements': [],
            'validations': [],
            'business_flows': []
        }
        
        lines = content.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Identify sections (basic pattern matching)
            line_lower = line.lower()
            
            if any(keyword in line_lower for keyword in ['chức năng', 'function', 'feature']):
                current_section = 'functions'
                info['functions'].append(line)
            elif any(keyword in line_lower for keyword in ['yêu cầu', 'requirement', 'spec']):
                current_section = 'requirements'
                info['requirements'].append(line)
            elif any(keyword in line_lower for keyword in ['validation', 'validate', 'kiểm tra']):
                current_section = 'validations'
                info['validations'].append(line)
            elif any(keyword in line_lower for keyword in ['luồng', 'flow', 'workflow']):
                current_section = 'business_flows'
                info['business_flows'].append(line)
            elif current_section:
                # Add to current section
                info[current_section].append(line)
        
        return info
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get basic file information"""
        if not os.path.exists(file_path):
            return {}
        
        stat = os.stat(file_path)
        return {
            'size': stat.st_size,
            'modified': stat.st_mtime,
            'exists': True
        }
