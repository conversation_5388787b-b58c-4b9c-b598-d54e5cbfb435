"""
Output Handler for Test Case Generator
Handles generation of .txt and Excel files with test cases
"""

import os
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Tuple
import uuid


class OutputHandler:
    """Handles output file generation and management"""
    
    def __init__(self, output_dir: str = "outputs"):
        self.output_dir = output_dir
        self.ensure_output_directory()
    
    def ensure_output_directory(self):
        """Ensure output directory exists"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def generate_filename(self, project_name: str, file_type: str) -> str:
        """Generate unique filename for output files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_project_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_project_name = safe_project_name.replace(' ', '_')
        
        if not safe_project_name:
            safe_project_name = "project"
        
        return f"{safe_project_name}_testcases_{timestamp}.{file_type}"
    
    def save_txt_file(self, test_cases: List[Dict[str, str]], project_name: str) -> str:
        """
        Save test cases to a formatted text file
        
        Args:
            test_cases: List of test case dictionaries
            project_name: Name of the project
            
        Returns:
            Path to the saved file
        """
        filename = self.generate_filename(project_name, "txt")
        file_path = os.path.join(self.output_dir, filename)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write(f"TEST CASES - {project_name.upper()}\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
            
            current_category = None
            test_case_number = 1
            
            for tc in test_cases:
                tc_id = tc.get('ID', '').strip()
                purpose = tc.get('Mục đích kiểm thử', '').strip()
                steps = tc.get('Các bước thực hiện', '').strip()
                expected = tc.get('Kết quả mong muốn', '').strip()
                priority = tc.get('Độ ưu tiên', '').strip()
                
                # Check if this is a category header
                if not tc_id and purpose and not steps and not expected:
                    if current_category:
                        f.write("\n")
                    current_category = purpose
                    f.write(f"\n{'=' * 60}\n")
                    f.write(f"{purpose.upper()}\n")
                    f.write(f"{'=' * 60}\n\n")
                    test_case_number = 1
                    continue
                
                # Skip empty rows
                if not any([tc_id, purpose, steps, expected]):
                    continue
                
                # Write test case
                f.write(f"Test Case #{test_case_number}\n")
                f.write(f"{'-' * 40}\n")
                f.write(f"ID: {tc_id}\n")
                f.write(f"Mục đích kiểm thử: {purpose}\n")
                f.write(f"Độ ưu tiên: {priority}\n\n")
                
                f.write("Các bước thực hiện:\n")
                if steps:
                    # Format steps with proper line breaks
                    formatted_steps = steps.replace('\\n', '\n')
                    for i, step in enumerate(formatted_steps.split('\n'), 1):
                        if step.strip():
                            f.write(f"  {step.strip()}\n")
                f.write("\n")
                
                f.write("Kết quả mong muốn:\n")
                if expected:
                    formatted_expected = expected.replace('\\n', '\n')
                    for line in formatted_expected.split('\n'):
                        if line.strip():
                            f.write(f"  {line.strip()}\n")
                f.write("\n")
                
                f.write("-" * 80 + "\n\n")
                test_case_number += 1
            
            f.write(f"\nTotal Test Cases Generated: {len([tc for tc in test_cases if tc.get('ID', '').strip()])}\n")
            f.write(f"Generated by Test Case Generator v1.0\n")
        
        return file_path
    
    def save_excel_file(self, test_cases: List[Dict[str, str]], project_name: str) -> str:
        """
        Save test cases to an Excel file
        
        Args:
            test_cases: List of test case dictionaries
            project_name: Name of the project
            
        Returns:
            Path to the saved file
        """
        filename = self.generate_filename(project_name, "xlsx")
        file_path = os.path.join(self.output_dir, filename)
        
        # Prepare data for DataFrame
        df_data = []
        for tc in test_cases:
            # Format steps and expected results for better readability in Excel
            steps = tc.get('Các bước thực hiện', '').replace('\\n', '\n')
            expected = tc.get('Kết quả mong muốn', '').replace('\\n', '\n')
            
            df_data.append({
                'ID': tc.get('ID', ''),
                'Mục đích kiểm thử': tc.get('Mục đích kiểm thử', ''),
                'Các bước thực hiện': steps,
                'Kết quả mong muốn': expected,
                'Độ ưu tiên': tc.get('Độ ưu tiên', '')
            })
        
        # Create DataFrame
        df = pd.DataFrame(df_data)
        
        # Create Excel writer with formatting
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Test Cases', index=False)
            
            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['Test Cases']
            
            # Apply formatting
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
            from openpyxl.utils import get_column_letter
            
            # Header formatting
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # Apply header formatting
            for col in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # Data formatting
            data_alignment = Alignment(horizontal="left", vertical="top", wrap_text=True)
            category_fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
            category_font = Font(bold=True)
            
            # Apply data formatting and identify category rows
            for row in range(2, len(df) + 2):
                for col in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row, column=col)
                    cell.alignment = data_alignment
                    
                    # Check if this is a category row (empty ID, has purpose, empty steps/expected)
                    row_data = df.iloc[row-2]
                    if (not str(row_data['ID']).strip() and 
                        str(row_data['Mục đích kiểm thử']).strip() and 
                        not str(row_data['Các bước thực hiện']).strip() and 
                        not str(row_data['Kết quả mong muốn']).strip()):
                        cell.fill = category_fill
                        cell.font = category_font
            
            # Adjust column widths
            column_widths = {
                'A': 15,  # ID
                'B': 40,  # Mục đích kiểm thử
                'C': 60,  # Các bước thực hiện
                'D': 60,  # Kết quả mong muốn
                'E': 12   # Độ ưu tiên
            }
            
            for col_letter, width in column_widths.items():
                worksheet.column_dimensions[col_letter].width = width
            
            # Set row heights for better readability
            for row in range(2, len(df) + 2):
                worksheet.row_dimensions[row].height = 60
            
            # Add project information sheet
            info_data = {
                'Thông tin': ['Tên dự án', 'Ngày tạo', 'Tổng số test case', 'Phiên bản'],
                'Giá trị': [
                    project_name,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    len([tc for tc in test_cases if tc.get('ID', '').strip()]),
                    '1.0'
                ]
            }
            info_df = pd.DataFrame(info_data)
            info_df.to_excel(writer, sheet_name='Thông tin dự án', index=False)
        
        return file_path
    
    def generate_output_files(self, test_cases: List[Dict[str, str]], project_name: str) -> Tuple[str, str]:
        """
        Generate both TXT and Excel output files
        
        Args:
            test_cases: List of test case dictionaries
            project_name: Name of the project
            
        Returns:
            Tuple of (txt_file_path, excel_file_path)
        """
        txt_path = self.save_txt_file(test_cases, project_name)
        excel_path = self.save_excel_file(test_cases, project_name)
        
        return txt_path, excel_path
    
    def format_test_cases_for_display(self, test_cases: List[Dict[str, str]]) -> str:
        """
        Format test cases for display in the UI
        
        Args:
            test_cases: List of test case dictionaries
            
        Returns:
            Formatted string for display
        """
        if not test_cases:
            return "Không có test case nào được tạo."
        
        display_lines = []
        current_category = None
        test_case_number = 1
        
        for tc in test_cases:
            tc_id = tc.get('ID', '').strip()
            purpose = tc.get('Mục đích kiểm thử', '').strip()
            steps = tc.get('Các bước thực hiện', '').strip()
            expected = tc.get('Kết quả mong muốn', '').strip()
            priority = tc.get('Độ ưu tiên', '').strip()
            
            # Check if this is a category header
            if not tc_id and purpose and not steps and not expected:
                if current_category:
                    display_lines.append("")
                current_category = purpose
                display_lines.append(f"\n{'='*50}")
                display_lines.append(f"{purpose.upper()}")
                display_lines.append(f"{'='*50}")
                test_case_number = 1
                continue
            
            # Skip empty rows
            if not any([tc_id, purpose, steps, expected]):
                continue
            
            # Format test case for display
            display_lines.append(f"\n[{tc_id}] {purpose}")
            display_lines.append(f"Độ ưu tiên: {priority}")
            
            if steps:
                display_lines.append("Các bước thực hiện:")
                formatted_steps = steps.replace('\\n', '\n')
                for step in formatted_steps.split('\n'):
                    if step.strip():
                        display_lines.append(f"  • {step.strip()}")
            
            if expected:
                display_lines.append("Kết quả mong muốn:")
                formatted_expected = expected.replace('\\n', '\n')
                for line in formatted_expected.split('\n'):
                    if line.strip():
                        display_lines.append(f"  → {line.strip()}")
            
            display_lines.append("-" * 60)
            test_case_number += 1
        
        total_test_cases = len([tc for tc in test_cases if tc.get('ID', '').strip()])
        display_lines.append(f"\nTổng cộng: {total_test_cases} test cases")
        
        return "\n".join(display_lines)
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about a generated file"""
        if not os.path.exists(file_path):
            return {'exists': False}
        
        stat = os.stat(file_path)
        return {
            'exists': True,
            'size': stat.st_size,
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            'filename': os.path.basename(file_path)
        }
