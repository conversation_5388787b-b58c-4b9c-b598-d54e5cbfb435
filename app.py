"""
Test Case Generator Chatbot Application
A Gradio-based application for generating comprehensive test cases
"""

import gradio as gr
import os
import tempfile
from typing import List, Dict, Any, Optional, Tu<PERSON>

from project_manager import ProjectManager
from file_parser import FileParser
from test_case_generator import TestCaseGenerator
from output_handler import OutputHandler


class TestCaseGeneratorApp:
    """Main application class for the Test Case Generator"""
    
    def __init__(self):
        self.project_manager = ProjectManager()
        self.file_parser = FileParser()
        self.test_generator = TestCaseGenerator()
        self.output_handler = OutputHandler()
        
        # Application state
        self.current_project_id = None
        self.uploaded_files = []
        self.generated_test_cases = []
        self.output_files = {'txt': None, 'excel': None}
    
    def create_project(self, name: str, description: str) -> Tuple[str, str]:
        """Create a new project"""
        try:
            if not name.strip():
                return "❌ Project name cannot be empty!", ""

            project_id = self.project_manager.create_project(name.strip(), description.strip())
            self.current_project_id = project_id

            # Update project dropdown
            project_names = self.project_manager.get_project_names()

            return f"✅ Successfully created project '{name}'!", gr.Dropdown(choices=project_names, value=name)

        except ValueError as e:
            return f"❌ Error: {str(e)}", gr.Dropdown()
        except Exception as e:
            return f"❌ Unexpected error: {str(e)}", gr.Dropdown()
    
    def select_project(self, project_name: str) -> str:
        """Select an existing project"""
        if not project_name:
            self.current_project_id = None
            return "⚠️ No project selected."

        project = self.project_manager.get_project_by_name(project_name)
        if project:
            self.current_project_id = project['id']
            files_count = len(project.get('files', []))
            outputs_count = len(project.get('generated_outputs', []))
            return f"✅ Selected project '{project_name}'. Contains {files_count} uploaded file(s) and {outputs_count} generated output(s)."
        else:
            self.current_project_id = None
            return f"❌ Project '{project_name}' not found."
    
    def upload_files(self, files) -> str:
        """Handle file uploads"""
        if not self.current_project_id:
            return "❌ Please select or create a project before uploading files."

        if not files:
            return "❌ No files selected for upload."

        uploaded_count = 0
        error_messages = []

        for file in files:
            try:
                if not self.file_parser.is_supported_file(file.name):
                    error_messages.append(f"File '{file.name}' format not supported.")
                    continue

                # Save file to upload directory
                saved_path, file_type = self.file_parser.save_uploaded_file(file, file.name)

                # Add to project
                self.project_manager.add_file_to_project(
                    self.current_project_id,
                    saved_path,
                    file.name,
                    file_type
                )

                uploaded_count += 1

            except Exception as e:
                error_messages.append(f"Upload error for '{file.name}': {str(e)}")

        result_message = f"✅ Successfully uploaded {uploaded_count} file(s)."
        if error_messages:
            result_message += f"\n⚠️ Issues encountered:\n" + "\n".join(error_messages)

        return result_message
    
    def generate_test_cases(self, ai_model: str) -> Tuple[str, str, str, str, str]:
        """Generate test cases from uploaded files"""
        if not self.current_project_id:
            return "❌ Please select a project first.", None, None, None, None

        project = self.project_manager.get_project(self.current_project_id)
        if not project:
            return "❌ Project not found.", None, None, None, None

        files = project.get('files', [])
        if not files:
            return "❌ No files uploaded. Please upload requirement documents first.", None, None, None, None
        
        try:
            # Parse all uploaded files and combine content
            combined_content = []
            
            for file_info in files:
                file_path = file_info['path']
                file_type = file_info['file_type']
                
                if os.path.exists(file_path):
                    content = self.file_parser.parse_file(file_path, file_type)
                    combined_content.append(f"=== File: {file_info['original_name']} ===\n{content}\n")
            
            if not combined_content:
                return "❌ Unable to read content from uploaded files.", None, None, None, None

            # Combine all content
            full_content = "\n".join(combined_content)

            # Generate test cases using selected AI model
            selected_model = None if ai_model == "Mock (Built-in Generator)" else ai_model
            self.generated_test_cases = self.test_generator.generate_test_cases(full_content, selected_model)
            
            if not self.generated_test_cases:
                return "❌ Unable to generate test cases from file content.", None, None, None, None

            # Generate output files
            txt_path, excel_path = self.output_handler.generate_output_files(
                self.generated_test_cases,
                project['name']
            )

            # Save output file paths to project
            self.project_manager.add_generated_output(self.current_project_id, txt_path, excel_path)

            # Store for download
            self.output_files = {'txt': txt_path, 'excel': excel_path}

            # Format for display
            display_content = self.output_handler.format_test_cases_for_display(self.generated_test_cases)

            test_case_count = len([tc for tc in self.generated_test_cases if tc.get('ID', '').strip()])

            model_used = selected_model if selected_model else "Built-in Generator"

            return (
                f"✅ Successfully generated {test_case_count} professional test cases using {model_used}!",
                display_content,
                f"📁 Generated Files:\n• {os.path.basename(txt_path)} (Text Format)\n• {os.path.basename(excel_path)} (Excel Format)",
                txt_path,
                excel_path
            )

        except Exception as e:
            return f"❌ Error generating test cases: {str(e)}", None, None, None, None
    
    def download_txt_file(self):
        """Return path to TXT file for download"""
        if self.output_files.get('txt') and os.path.exists(self.output_files['txt']):
            return self.output_files['txt']
        return None
    
    def download_excel_file(self):
        """Return path to Excel file for download"""
        if self.output_files.get('excel') and os.path.exists(self.output_files['excel']):
            return self.output_files['excel']
        return None
    
    def get_project_choices(self):
        """Get current project choices for dropdown"""
        return self.project_manager.get_project_names()
    
    def create_interface(self):
        """Create the Gradio interface"""
        with gr.Blocks(
            title="Professional Test Case Generator",
            theme=gr.themes.Soft(),
            css="""
            /* Professional Color Scheme */
            .main-header {
                text-align: center;
                color: #1e3a8a;
                margin-bottom: 2rem;
                padding: 1.5rem;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 1px solid #cbd5e1;
            }
            .section-header {
                color: #1e40af;
                border-bottom: 3px solid #3b82f6;
                padding-bottom: 0.75rem;
                margin: 2rem 0 1.5rem 0;
                font-weight: 600;
                font-size: 1.25rem;
            }
            .professional-card {
                background: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            }
            .status-success {
                background-color: #dcfce7;
                border: 1px solid #bbf7d0;
                color: #166534;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
            }
            .status-error {
                background-color: #fef2f2;
                border: 1px solid #fecaca;
                color: #dc2626;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
            }
            .status-info {
                background-color: #eff6ff;
                border: 1px solid #bfdbfe;
                color: #1d4ed8;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
            }
            .ai-status-card {
                background: #f8fafc;
                border: 1px solid #cbd5e1;
                border-radius: 8px;
                padding: 1rem;
                margin: 1rem 0;
            }
            .divider {
                height: 1px;
                background: linear-gradient(to right, transparent, #cbd5e1, transparent);
                margin: 2rem 0;
            }
            /* Button Styling */
            .primary-btn {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border: none;
                color: white;
                font-weight: 600;
            }
            .secondary-btn {
                background: linear-gradient(135deg, #64748b 0%, #475569 100%);
                border: none;
                color: white;
                font-weight: 500;
            }
            """
        ) as interface:

            # Header
            gr.Markdown(
                """
                # 🚀 Professional Test Case Generator
                ### AI-Powered Vietnamese Test Case Generation Platform

                **Supported Formats:** .txt, .docx, .xlsx, .xls | **Output Language:** Vietnamese | **Quality:** Enterprise-Grade Test Cases
                """,
                elem_classes=["main-header"]
            )

            # Divider
            gr.HTML('<div class="divider"></div>')
            
            with gr.Row(equal_height=True):
                with gr.Column(scale=1):
                    # Project Management Section
                    gr.Markdown("## 📁 Project Management", elem_classes=["section-header"])

                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 🆕 Create New Project")
                        project_name_input = gr.Textbox(
                            label="Project Name",
                            placeholder="Enter project name...",
                            max_lines=1,
                            info="Choose a descriptive name for your test case project"
                        )
                        project_desc_input = gr.Textbox(
                            label="Project Description",
                            placeholder="Brief description of the project scope...",
                            lines=2,
                            info="Optional: Add context about the testing requirements"
                        )
                        create_project_btn = gr.Button(
                            "🆕 Create Project",
                            variant="primary",
                            size="lg",
                            elem_classes=["primary-btn"]
                        )

                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 📂 Select Existing Project")
                        project_dropdown = gr.Dropdown(
                            label="Available Projects",
                            choices=self.get_project_choices(),
                            interactive=True,
                            info="Choose from your existing projects"
                        )
                        select_project_btn = gr.Button(
                            "📂 Select Project",
                            variant="secondary",
                            size="lg",
                            elem_classes=["secondary-btn"]
                        )

                    project_status = gr.Textbox(
                        label="Project Status",
                        interactive=False,
                        lines=3,
                        info="Current project information and file count"
                    )

                with gr.Column(scale=2):
                    # File Upload Section
                    gr.Markdown("## 📤 Document Upload", elem_classes=["section-header"])

                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 📋 Requirements Documents")
                        file_upload = gr.Files(
                            label="Select Requirement Files",
                            file_types=[".txt", ".docx", ".xlsx", ".xls"],
                            file_count="multiple",
                            info="Upload functional requirements, specifications, or user stories"
                        )

                        gr.Markdown("""
                        **Supported Formats:**
                        - 📄 **Text Files** (.txt) - Plain text requirements
                        - 📝 **Word Documents** (.docx) - Formatted specifications
                        - 📊 **Excel Files** (.xlsx, .xls) - Structured requirements
                        """)

                        upload_btn = gr.Button(
                            "📤 Upload Documents",
                            variant="primary",
                            size="lg",
                            elem_classes=["primary-btn"]
                        )

                    upload_status = gr.Textbox(
                        label="Upload Status",
                        interactive=False,
                        lines=4,
                        info="File upload progress and validation results"
                    )

            # Divider
            gr.HTML('<div class="divider"></div>')

            # Test Case Generation Section
            gr.Markdown("## 🤖 AI-Powered Test Case Generation", elem_classes=["section-header"])

            with gr.Row():
                with gr.Column(scale=1):
                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### ⚙️ Generation Settings")
                        ai_model_dropdown = gr.Dropdown(
                            label="AI Model Selection",
                            choices=self.test_generator.get_available_ai_models() + ["Mock (Built-in Generator)"],
                            value="Mock (Built-in Generator)" if not self.test_generator.get_available_ai_models() else self.test_generator.get_available_ai_models()[0],
                            interactive=True,
                            info="Choose your preferred AI model for test case generation"
                        )

                with gr.Column(scale=2):
                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 🚀 Generate Professional Test Cases")
                        gr.Markdown("Click the button below to generate comprehensive Vietnamese test cases from your uploaded requirements.")
                        generate_btn = gr.Button(
                            "🚀 Generate Test Cases",
                            variant="primary",
                            size="lg",
                            elem_classes=["primary-btn"]
                        )

            # AI Service Status
            with gr.Group(elem_classes=["ai-status-card"]):
                ai_status = self.test_generator.get_ai_service_status()
                status_text = "🔧 **AI Services Status:**\n\n"

                if ai_status['openai']['available']:
                    status_text += f"✅ **ChatGPT**: Ready (Model: {ai_status['openai']['model']})\n"
                elif ai_status['openai']['configured']:
                    status_text += "⚠️ **ChatGPT**: Configured but unavailable\n"
                else:
                    status_text += "❌ **ChatGPT**: API key not configured\n"

                if ai_status['gemini']['available']:
                    status_text += f"✅ **Gemini**: Ready (Model: {ai_status['gemini']['model']})\n"
                elif ai_status['gemini']['configured']:
                    status_text += "⚠️ **Gemini**: Configured but unavailable\n"
                else:
                    status_text += "❌ **Gemini**: API key not configured\n"

                if not ai_status['openai']['available'] and not ai_status['gemini']['available']:
                    status_text += "\n💡 **Setup Guide:** Create a `.env` file and add your API keys to enable AI-powered generation"
                    status_text += "\n📖 See README.md for detailed configuration instructions"

                gr.Markdown(status_text)

            generation_status = gr.Textbox(
                label="Generation Status",
                interactive=False,
                lines=3,
                info="Real-time status of test case generation process"
            )

            # Divider
            gr.HTML('<div class="divider"></div>')

            # Output Section
            gr.Markdown("## 📋 Generated Test Cases", elem_classes=["section-header"])

            with gr.Group(elem_classes=["professional-card"]):
                gr.Markdown("### 📝 Professional Vietnamese Test Cases")
                gr.Markdown("Generated test cases will appear below with proper structure: ID, Purpose, Steps, Expected Results, and Priority.")

                test_cases_output = gr.Textbox(
                    label="Generated Test Cases",
                    lines=25,
                    max_lines=35,
                    interactive=False,
                    show_copy_button=True,
                    info="Copy these test cases to your clipboard or download as files"
                )

            # Divider
            gr.HTML('<div class="divider"></div>')

            # Download Section
            gr.Markdown("## 💾 Export & Download", elem_classes=["section-header"])

            with gr.Group(elem_classes=["professional-card"]):
                gr.Markdown("### 📁 Download Generated Files")
                gr.Markdown("Export your test cases in multiple formats for easy integration with your testing workflow.")

                with gr.Row():
                    download_txt_btn = gr.DownloadButton(
                        "📄 Download TXT File",
                        variant="secondary",
                        size="lg",
                        elem_classes=["secondary-btn"]
                    )
                    download_excel_btn = gr.DownloadButton(
                        "📊 Download Excel File",
                        variant="secondary",
                        size="lg",
                        elem_classes=["secondary-btn"]
                    )

            download_info = gr.Textbox(
                label="File Information",
                interactive=False,
                lines=3,
                info="Details about generated files and download status"
            )
            
            # Event handlers
            create_project_btn.click(
                fn=self.create_project,
                inputs=[project_name_input, project_desc_input],
                outputs=[project_status, project_dropdown]
            )
            
            select_project_btn.click(
                fn=self.select_project,
                inputs=[project_dropdown],
                outputs=[project_status]
            )
            
            upload_btn.click(
                fn=self.upload_files,
                inputs=[file_upload],
                outputs=[upload_status]
            )
            
            generate_btn.click(
                fn=self.generate_test_cases,
                inputs=[ai_model_dropdown],
                outputs=[generation_status, test_cases_output, download_info, download_txt_btn, download_excel_btn]
            )
            
            # Download buttons are handled automatically by Gradio when file paths are set
            
            # Divider
            gr.HTML('<div class="divider"></div>')

            # Footer
            gr.Markdown(
                """
                ---
                **Professional Test Case Generator v2.0** | Enterprise-Grade AI-Powered Testing Solution
                🚀 **Features:** Multi-AI Support • Vietnamese Output • Professional Formatting • Export Options
                💡 **Powered by:** OpenAI ChatGPT • Google Gemini • Gradio Framework
                """,
                elem_classes=["main-header"]
            )
        
        return interface


def main():
    """Main function to run the application"""
    app = TestCaseGeneratorApp()
    interface = app.create_interface()
    
    # Launch the application
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True,
        inbrowser=True
    )


if __name__ == "__main__":
    main()
