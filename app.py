"""
Test Case Generator Chatbot Application
A Gradio-based application for generating comprehensive test cases
"""

import gradio as gr
import os
import tempfile
from typing import List, Dict, Any, Optional, Tuple

from project_manager import ProjectManager
from file_parser import FileParser
from test_case_generator import TestCaseGenerator
from output_handler import OutputHandler


class TestCaseGeneratorApp:
    """Main application class for the Test Case Generator"""
    
    def __init__(self):
        self.project_manager = ProjectManager()
        self.file_parser = FileParser()
        self.test_generator = TestCaseGenerator()
        self.output_handler = OutputHandler()
        
        # Application state
        self.current_project_id = None
        self.uploaded_files = []
        self.generated_test_cases = []
        self.output_files = {'txt': None, 'excel': None}
    
    def create_project(self, name: str, description: str) -> Tuple[str, str]:
        """Create a new project"""
        try:
            if not name.strip():
                return "❌ Tên dự án không được để trống!", ""
            
            project_id = self.project_manager.create_project(name.strip(), description.strip())
            self.current_project_id = project_id
            
            # Update project dropdown
            project_names = self.project_manager.get_project_names()
            
            return f"✅ Đã tạo dự án '{name}' thành công!", gr.Dropdown(choices=project_names, value=name)
        
        except ValueError as e:
            return f"❌ Lỗi: {str(e)}", gr.Dropdown()
        except Exception as e:
            return f"❌ Lỗi không xác định: {str(e)}", gr.Dropdown()
    
    def select_project(self, project_name: str) -> str:
        """Select an existing project"""
        if not project_name:
            self.current_project_id = None
            return "⚠️ Chưa chọn dự án nào."
        
        project = self.project_manager.get_project_by_name(project_name)
        if project:
            self.current_project_id = project['id']
            files_count = len(project.get('files', []))
            return f"✅ Đã chọn dự án '{project_name}'. Có {files_count} file đã upload."
        else:
            self.current_project_id = None
            return f"❌ Không tìm thấy dự án '{project_name}'."
    
    def upload_files(self, files) -> str:
        """Handle file uploads"""
        if not self.current_project_id:
            return "❌ Vui lòng chọn hoặc tạo dự án trước khi upload file."
        
        if not files:
            return "❌ Không có file nào được chọn."
        
        uploaded_count = 0
        error_messages = []
        
        for file in files:
            try:
                if not self.file_parser.is_supported_file(file.name):
                    error_messages.append(f"File '{file.name}' không được hỗ trợ.")
                    continue
                
                # Save file to upload directory
                saved_path, file_type = self.file_parser.save_uploaded_file(file, file.name)
                
                # Add to project
                self.project_manager.add_file_to_project(
                    self.current_project_id, 
                    saved_path, 
                    file.name, 
                    file_type
                )
                
                uploaded_count += 1
                
            except Exception as e:
                error_messages.append(f"Lỗi upload '{file.name}': {str(e)}")
        
        result_message = f"✅ Đã upload thành công {uploaded_count} file."
        if error_messages:
            result_message += f"\n⚠️ Lỗi:\n" + "\n".join(error_messages)
        
        return result_message
    
    def generate_test_cases(self) -> Tuple[str, str, str, str, str]:
        """Generate test cases from uploaded files"""
        if not self.current_project_id:
            return "❌ Vui lòng chọn dự án trước.", None, None, None, None

        project = self.project_manager.get_project(self.current_project_id)
        if not project:
            return "❌ Không tìm thấy dự án.", None, None, None, None

        files = project.get('files', [])
        if not files:
            return "❌ Chưa có file nào được upload. Vui lòng upload file trước.", None, None, None, None
        
        try:
            # Parse all uploaded files and combine content
            combined_content = []
            
            for file_info in files:
                file_path = file_info['path']
                file_type = file_info['file_type']
                
                if os.path.exists(file_path):
                    content = self.file_parser.parse_file(file_path, file_type)
                    combined_content.append(f"=== File: {file_info['original_name']} ===\n{content}\n")
            
            if not combined_content:
                return "❌ Không thể đọc nội dung từ các file đã upload.", None, None, None, None
            
            # Combine all content
            full_content = "\n".join(combined_content)
            
            # Generate test cases
            self.generated_test_cases = self.test_generator.generate_test_cases(full_content)
            
            if not self.generated_test_cases:
                return "❌ Không thể tạo test case từ nội dung file.", None, None, None, None
            
            # Generate output files
            txt_path, excel_path = self.output_handler.generate_output_files(
                self.generated_test_cases, 
                project['name']
            )
            
            # Save output file paths to project
            self.project_manager.add_generated_output(self.current_project_id, txt_path, excel_path)
            
            # Store for download
            self.output_files = {'txt': txt_path, 'excel': excel_path}
            
            # Format for display
            display_content = self.output_handler.format_test_cases_for_display(self.generated_test_cases)
            
            test_case_count = len([tc for tc in self.generated_test_cases if tc.get('ID', '').strip()])
            
            return (
                f"✅ Đã tạo thành công {test_case_count} test cases!",
                display_content,
                f"📁 Files đã tạo:\n• {os.path.basename(txt_path)}\n• {os.path.basename(excel_path)}",
                txt_path,
                excel_path
            )
            
        except Exception as e:
            return f"❌ Lỗi khi tạo test cases: {str(e)}", None, None, None, None
    
    def download_txt_file(self):
        """Return path to TXT file for download"""
        if self.output_files.get('txt') and os.path.exists(self.output_files['txt']):
            return self.output_files['txt']
        return None
    
    def download_excel_file(self):
        """Return path to Excel file for download"""
        if self.output_files.get('excel') and os.path.exists(self.output_files['excel']):
            return self.output_files['excel']
        return None
    
    def get_project_choices(self):
        """Get current project choices for dropdown"""
        return self.project_manager.get_project_names()
    
    def create_interface(self):
        """Create the Gradio interface"""
        with gr.Blocks(
            title="Test Case Generator",
            theme=gr.themes.Soft(),
            css="""
            .main-header { text-align: center; color: #2c3e50; margin-bottom: 2rem; }
            .section-header { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 0.5rem; margin: 1.5rem 0 1rem 0; }
            .status-message { padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; }
            .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
            .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
            """
        ) as interface:
            
            # Header
            gr.Markdown(
                """
                # 🤖 Test Case Generator Chatbot
                ### Hệ thống tự động tạo test case từ tài liệu yêu cầu chức năng
                
                **Hỗ trợ:** .txt, .docx, .xlsx, .xls | **Ngôn ngữ:** Tiếng Việt | **Định dạng:** Professional Test Cases
                """,
                elem_classes=["main-header"]
            )
            
            with gr.Row():
                with gr.Column(scale=1):
                    # Project Management Section
                    gr.Markdown("## 📁 Quản lý Dự án", elem_classes=["section-header"])
                    
                    with gr.Group():
                        gr.Markdown("### Tạo dự án mới")
                        project_name_input = gr.Textbox(
                            label="Tên dự án",
                            placeholder="Nhập tên dự án...",
                            max_lines=1
                        )
                        project_desc_input = gr.Textbox(
                            label="Mô tả dự án",
                            placeholder="Mô tả ngắn gọn về dự án...",
                            lines=2
                        )
                        create_project_btn = gr.Button("🆕 Tạo dự án", variant="primary")
                    
                    with gr.Group():
                        gr.Markdown("### Chọn dự án hiện có")
                        project_dropdown = gr.Dropdown(
                            label="Dự án",
                            choices=self.get_project_choices(),
                            interactive=True
                        )
                        select_project_btn = gr.Button("📂 Chọn dự án", variant="secondary")
                    
                    project_status = gr.Textbox(
                        label="Trạng thái dự án",
                        interactive=False,
                        lines=2
                    )
                
                with gr.Column(scale=2):
                    # File Upload Section
                    gr.Markdown("## 📤 Upload Files", elem_classes=["section-header"])
                    
                    file_upload = gr.Files(
                        label="Chọn files (.txt, .docx, .xlsx, .xls)",
                        file_types=[".txt", ".docx", ".xlsx", ".xls"],
                        file_count="multiple"
                    )
                    
                    upload_btn = gr.Button("📤 Upload Files", variant="primary", size="lg")
                    upload_status = gr.Textbox(
                        label="Trạng thái upload",
                        interactive=False,
                        lines=3
                    )
            
            # Test Case Generation Section
            gr.Markdown("## 🔄 Tạo Test Cases", elem_classes=["section-header"])
            
            with gr.Row():
                with gr.Column(scale=2):
                    generate_btn = gr.Button(
                        "🚀 Generate Test Cases",
                        variant="primary",
                        size="lg"
                    )
                with gr.Column(scale=1):
                    gr.Markdown("*Nhấn để tạo test cases từ nội dung files đã upload*")
            
            generation_status = gr.Textbox(
                label="Trạng thái tạo test cases",
                interactive=False,
                lines=2
            )
            
            # Output Section
            gr.Markdown("## 📋 Kết quả Test Cases", elem_classes=["section-header"])
            
            test_cases_output = gr.Textbox(
                label="Test Cases đã tạo",
                lines=20,
                max_lines=30,
                interactive=False,
                show_copy_button=True
            )
            
            # Download Section
            gr.Markdown("## 💾 Tải xuống Files", elem_classes=["section-header"])
            
            with gr.Row():
                download_txt_btn = gr.DownloadButton(
                    "📄 Tải file TXT",
                    variant="secondary"
                )
                download_excel_btn = gr.DownloadButton(
                    "📊 Tải file Excel",
                    variant="secondary"
                )
            
            download_info = gr.Textbox(
                label="Thông tin files",
                interactive=False,
                lines=3
            )
            
            # Event handlers
            create_project_btn.click(
                fn=self.create_project,
                inputs=[project_name_input, project_desc_input],
                outputs=[project_status, project_dropdown]
            )
            
            select_project_btn.click(
                fn=self.select_project,
                inputs=[project_dropdown],
                outputs=[project_status]
            )
            
            upload_btn.click(
                fn=self.upload_files,
                inputs=[file_upload],
                outputs=[upload_status]
            )
            
            generate_btn.click(
                fn=self.generate_test_cases,
                inputs=[],
                outputs=[generation_status, test_cases_output, download_info, download_txt_btn, download_excel_btn]
            )
            
            # Download buttons are handled automatically by Gradio when file paths are set
            
            # Footer
            gr.Markdown(
                """
                ---
                **Test Case Generator v1.0** | Developed with ❤️ using Gradio | 
                Supports Vietnamese test case generation with professional formatting
                """,
                elem_classes=["main-header"]
            )
        
        return interface


def main():
    """Main function to run the application"""
    app = TestCaseGeneratorApp()
    interface = app.create_interface()
    
    # Launch the application
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True,
        inbrowser=True
    )


if __name__ == "__main__":
    main()
