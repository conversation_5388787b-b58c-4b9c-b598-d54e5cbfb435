"""
Project Management System for Test Case Generator
Handles project creation, storage, and file management
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid


class ProjectManager:
    """Manages projects and their associated files"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.projects_file = os.path.join(data_dir, "projects.json")
        self.ensure_data_directory()
        self.projects = self.load_projects()
    
    def ensure_data_directory(self):
        """Ensure data directory exists"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def load_projects(self) -> Dict[str, Any]:
        """Load projects from JSON file"""
        if os.path.exists(self.projects_file):
            try:
                with open(self.projects_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def save_projects(self):
        """Save projects to JSON file"""
        with open(self.projects_file, 'w', encoding='utf-8') as f:
            json.dump(self.projects, f, ensure_ascii=False, indent=2)
    
    def create_project(self, name: str, description: str = "") -> str:
        """
        Create a new project
        
        Args:
            name: Project name
            description: Project description
            
        Returns:
            project_id: Unique project identifier
        """
        if not name.strip():
            raise ValueError("Project name cannot be empty")
        
        # Check if project name already exists
        for project_id, project in self.projects.items():
            if project['name'].lower() == name.lower():
                raise ValueError(f"Project '{name}' already exists")
        
        project_id = str(uuid.uuid4())
        project_data = {
            'id': project_id,
            'name': name.strip(),
            'description': description.strip(),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'files': [],
            'generated_outputs': []
        }
        
        self.projects[project_id] = project_data
        self.save_projects()
        
        return project_id
    
    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get project by ID"""
        return self.projects.get(project_id)
    
    def get_project_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get project by name"""
        for project in self.projects.values():
            if project['name'].lower() == name.lower():
                return project
        return None
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """List all projects"""
        return list(self.projects.values())
    
    def get_project_names(self) -> List[str]:
        """Get list of project names"""
        return [project['name'] for project in self.projects.values()]
    
    def update_project(self, project_id: str, name: str = None, description: str = None):
        """Update project details"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        project = self.projects[project_id]
        
        if name is not None:
            # Check if new name conflicts with existing projects
            for pid, p in self.projects.items():
                if pid != project_id and p['name'].lower() == name.lower():
                    raise ValueError(f"Project '{name}' already exists")
            project['name'] = name.strip()
        
        if description is not None:
            project['description'] = description.strip()
        
        project['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def delete_project(self, project_id: str):
        """Delete a project and its associated files"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        # Clean up associated files
        project = self.projects[project_id]
        for file_info in project.get('files', []):
            file_path = file_info.get('path')
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except OSError:
                    pass  # File might be in use or already deleted
        
        # Clean up generated outputs
        for output_info in project.get('generated_outputs', []):
            for file_path in [output_info.get('txt_path'), output_info.get('excel_path')]:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except OSError:
                        pass
        
        del self.projects[project_id]
        self.save_projects()
    
    def add_file_to_project(self, project_id: str, file_path: str, original_name: str, file_type: str):
        """Add a file to a project"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        file_info = {
            'path': file_path,
            'original_name': original_name,
            'file_type': file_type,
            'uploaded_at': datetime.now().isoformat()
        }
        
        self.projects[project_id]['files'].append(file_info)
        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def add_generated_output(self, project_id: str, txt_path: str, excel_path: str):
        """Add generated output files to a project"""
        if project_id not in self.projects:
            raise ValueError(f"Project with ID '{project_id}' not found")
        
        output_info = {
            'txt_path': txt_path,
            'excel_path': excel_path,
            'generated_at': datetime.now().isoformat()
        }
        
        self.projects[project_id]['generated_outputs'].append(output_info)
        self.projects[project_id]['updated_at'] = datetime.now().isoformat()
        self.save_projects()
    
    def get_project_files(self, project_id: str) -> List[Dict[str, Any]]:
        """Get all files associated with a project"""
        project = self.get_project(project_id)
        if not project:
            return []
        return project.get('files', [])
