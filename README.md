# Test Case Generator Chatbot

A comprehensive chatbot application built with Gradio that helps software testers generate professional test cases from functional requirements documents.

## 🌟 Features

### Project Management
- **Create Projects**: Create new projects with name and description
- **Project Storage**: Store project details in structured JSON format
- **File Association**: Associate uploaded files with specific projects
- **Project Selection**: Switch between existing projects

### File Upload & Parsing
- **Multiple Formats**: Support for .txt, .docx, and Excel (.xlsx, .xls) files
- **Content Extraction**: Parse and extract text content from all supported formats
- **Requirement Analysis**: Analyze functional requirements and business flows
- **Multi-file Support**: Upload and process multiple files per project

### Test Case Generation
- **Professional Prompts**: Uses expertly crafted prompts for comprehensive test case generation
- **Vietnamese Output**: Generates test cases in Vietnamese with professional terminology
- **Comprehensive Coverage**: Includes UI validation, functional testing, input validation, and edge cases
- **Structured Format**: Test cases include ID, Purpose, Steps, Expected Results, and Priority
- **Few-shot Learning**: Uses example-based generation for consistent quality

### Output Handling
- **Dual Format Output**: Generates both .txt and Excel (.xlsx) files
- **Professional Formatting**: Well-structured and readable output formats
- **Download Capability**: Direct download buttons for generated files
- **Display Preview**: View generated test cases in the interface before download

### User Interface
- **Intuitive Design**: Clean, responsive Gradio interface
- **Step-by-step Workflow**: Guided process from project creation to test case generation
- **Real-time Feedback**: Status updates and error messages throughout the process
- **Professional Styling**: Modern UI with proper color schemes and typography

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Installation

1. **Clone or download the project files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python app.py
   ```

4. **Open your browser** and navigate to `http://localhost:7860`

## 📖 Usage Guide

### Step 1: Project Management
1. **Create a new project**:
   - Enter project name and description
   - Click "🆕 Tạo dự án"
   
2. **Or select existing project**:
   - Choose from dropdown list
   - Click "📂 Chọn dự án"

### Step 2: Upload Files
1. Click on the file upload area
2. Select one or more files (.txt, .docx, .xlsx, .xls)
3. Click "📤 Upload Files"
4. Wait for upload confirmation

### Step 3: Generate Test Cases
1. Ensure you have selected a project and uploaded files
2. Click "🚀 Generate Test Cases"
3. Wait for generation to complete
4. Review generated test cases in the output area

### Step 4: Download Results
1. Click "📄 Tải file TXT" for text format
2. Click "📊 Tải file Excel" for spreadsheet format
3. Files will be downloaded to your default download folder

## 📁 Project Structure

```
TestBot/
├── app.py                 # Main Gradio application
├── project_manager.py     # Project management system
├── file_parser.py         # File upload and parsing
├── test_case_generator.py # Test case generation logic
├── output_handler.py      # Output file generation
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── data/                 # Project data storage
│   ├── projects.json     # Project metadata
│   └── uploads/          # Uploaded files
├── outputs/              # Generated test case files
└── templates/            # Template files (future use)
```

## 🔧 Configuration

### Supported File Types
- **Text files**: .txt
- **Word documents**: .docx
- **Excel spreadsheets**: .xlsx, .xls

### Output Formats
- **Text file**: Formatted, readable test cases with proper sections
- **Excel file**: Structured spreadsheet with columns for ID, Purpose, Steps, Expected Results, Priority

### Test Case Categories
- **UI Testing**: Interface validation and display checks
- **Validation Testing**: Input validation and error handling
- **Functional Testing**: Core functionality and business logic
- **Delete Operations**: Data deletion and confirmation flows
- **Search Functionality**: Search and filter operations

## 🎯 Example Input/Output

### Input Example (Vietnamese Requirements):
```
Chức năng cần kiểm thử (URD)
1.1. Mục đích: Quản lý chức danh
2.1. Tạo mới chức danh
- Tên chức danh (bắt buộc, tối đa 64 ký tự)
- Mô tả (không bắt buộc, tối đa 256 ký tự)
```

### Output Example:
```
TC_UI_001: Kiểm tra hiển thị popup Tạo mới chức danh
TC_VAL_001: Kiểm tra tạo mới với thông tin hợp lệ
TC_VAL_002: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
...
```

## 🛠️ Technical Details

### Dependencies
- **gradio**: Web interface framework
- **pandas**: Data manipulation and Excel handling
- **openpyxl**: Excel file operations
- **python-docx**: Word document parsing
- **xlrd**: Excel file reading

### Architecture
- **Modular Design**: Separate modules for different functionalities
- **Error Handling**: Comprehensive error handling and user feedback
- **File Management**: Secure file upload and storage
- **Data Persistence**: JSON-based project data storage

## 🔮 Future Enhancements

- **AI Integration**: Connect to OpenAI, Anthropic, or other AI services for enhanced generation
- **Template System**: Customizable test case templates
- **Export Options**: Additional export formats (PDF, CSV)
- **Collaboration**: Multi-user project sharing
- **Version Control**: Test case versioning and history
- **Integration**: API endpoints for external tool integration

## 🐛 Troubleshooting

### Common Issues
1. **File upload fails**: Check file format and size
2. **No test cases generated**: Ensure files contain readable requirements
3. **Download not working**: Check browser download settings
4. **Interface not loading**: Verify all dependencies are installed

### Error Messages
- **"Tên dự án không được để trống!"**: Enter a project name
- **"Chưa chọn dự án nào"**: Select or create a project first
- **"File không được hỗ trợ"**: Use supported file formats only

## 📄 License

This project is provided as-is for educational and professional use.

## 🤝 Contributing

Feel free to submit issues, feature requests, or improvements to enhance the application.

---

**Test Case Generator v1.0** | Professional Vietnamese Test Case Generation
