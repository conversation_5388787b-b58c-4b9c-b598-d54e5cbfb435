"""
Test Case Generation System
Generates comprehensive test cases based on functional requirements
"""

import re
from typing import List, Dict, Any, Optional
import random


class TestCaseGenerator:
    """Generates test cases from functional requirements"""
    
    def __init__(self):
        self.test_case_counter = 1
        self.categories = {
            'UI': 'Kiểm tra UI',
            'VAL': 'Kiểm tra validate',
            'FUN': 'Kiểm tra chức năng',
            'DEL': 'Kiểm tra xóa',
            'SRC': 'Kiểm tra tìm kiếm',
            'SEC': 'Kiểm tra bảo mật',
            'PER': 'Kiểm tra hiệu năng'
        }
    
    def create_professional_prompt(self, content: str) -> str:
        """
        Create a professional prompt for test case generation
        """
        prompt = f"""
You are a professional software testing expert specializing in comprehensive test case generation. Your task is to analyze the provided functional requirements and generate detailed, professional test cases in Vietnamese.

REQUIREMENTS TO ANALYZE:
{content}

INSTRUCTIONS:
1. Generate comprehensive test cases covering ALL aspects of the functionality
2. Include UI validation, functional testing, input validation, edge cases, and error scenarios
3. Structure test cases with proper categorization
4. Use professional Vietnamese terminology
5. Follow the exact format shown in the examples below

OUTPUT FORMAT:
Generate test cases in CSV format with columns: ID, <PERSON><PERSON>c đích kiểm thử, C<PERSON>c bước thực hiện, Kết quả mong muốn, Độ ưu tiên

EXAMPLE INPUT:
Chức năng cần kiểm thử (URD)
1.1. Mục đích: Tài liệu này mô tả yêu cầu của người dùng đối với chức năng Quản lý chức danh, bao gồm: Tạo mới, Sửa, Xóa và Tìm kiếm chức danh trong hệ thống.
2.1. Tạo mới chức danh
- Tên chức danh (bắt buộc, tối đa 64 ký tự, không trùng lặp)
- Mô tả (không bắt buộc, tối đa 256 ký tự)
- Nút Lưu: Thêm chức danh vào hệ thống
- Nút Hủy: Đóng popup không lưu

EXAMPLE OUTPUT:
ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên
,Kiểm tra UI,,,
TC_UI_001,Kiểm tra hiển thị popup Tạo mới chức danh,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút "Tạo mới chức danh",Popup "Tạo mới chức danh" hiển thị với các trường Tên chức danh, Mô tả và nút Lưu, Hủy,High
TC_UI_002,Kiểm tra hiển thị các trường bắt buộc,1. Mở popup Tạo mới chức danh \\n 2. Kiểm tra hiển thị dấu * cho trường bắt buộc,Trường "Tên chức danh" hiển thị dấu * hoặc nhãn "bắt buộc",High
,Kiểm tra validate trường Tạo mới chức danh,,,
TC_VAL_001,Kiểm tra tạo mới với thông tin hợp lệ,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Nhập Tên chức danh (ví dụ: "Quản lý") và Mô tả \\n 4. Nhấn Lưu,Chức danh được thêm thành công, hiển thị thông báo thành công,High
TC_VAL_002,Kiểm tra tạo mới khi bỏ trống Tên chức danh,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Để trống Tên chức danh \\n 4. Nhấn Lưu,Hiển thị lỗi "Vui lòng nhập Tên chức danh",High
TC_VAL_003,Kiểm tra tạo mới với Tên chức danh trùng lặp,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Nhập Tên chức danh đã tồn tại \\n 4. Nhấn Lưu,Hiển thị lỗi "Tên chức danh đã tồn tại",High
TC_VAL_004,Kiểm tra tạo mới với Tên chức danh vượt quá 64 ký tự,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Nhập Tên chức danh vượt quá 64 ký tự \\n 4. Nhấn Lưu,Hiển thị lỗi "Tên chức danh không được vượt quá 64 ký tự",Normal

IMPORTANT GUIDELINES:
- Generate test cases for ALL functions mentioned in the requirements
- Include boundary testing (minimum/maximum values)
- Cover positive and negative scenarios
- Include security and performance considerations where applicable
- Use proper Vietnamese terminology
- Ensure test steps are clear and actionable
- Set appropriate priority levels (High, Normal, Low)
- Group related test cases under category headers

Now generate comprehensive test cases for the provided requirements:
"""
        return prompt
    
    def generate_mock_test_cases(self, content: str) -> List[Dict[str, str]]:
        """
        Generate mock test cases based on content analysis
        This is a fallback when no AI service is available
        """
        test_cases = []
        
        # Analyze content to identify functions
        functions = self.extract_functions(content)
        
        for func in functions:
            # Generate UI test cases
            test_cases.extend(self.generate_ui_test_cases(func))
            # Generate validation test cases
            test_cases.extend(self.generate_validation_test_cases(func))
            # Generate functional test cases
            test_cases.extend(self.generate_functional_test_cases(func))
        
        return test_cases
    
    def extract_functions(self, content: str) -> List[Dict[str, Any]]:
        """Extract function information from content"""
        functions = []
        lines = content.split('\n')
        
        current_function = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Look for function indicators
            if any(keyword in line.lower() for keyword in ['tạo mới', 'thêm', 'create', 'add']):
                current_function = {
                    'name': 'Tạo mới',
                    'type': 'create',
                    'fields': [],
                    'validations': []
                }
                functions.append(current_function)
            elif any(keyword in line.lower() for keyword in ['sửa', 'chỉnh sửa', 'edit', 'update']):
                current_function = {
                    'name': 'Sửa',
                    'type': 'update',
                    'fields': [],
                    'validations': []
                }
                functions.append(current_function)
            elif any(keyword in line.lower() for keyword in ['xóa', 'delete', 'remove']):
                current_function = {
                    'name': 'Xóa',
                    'type': 'delete',
                    'fields': [],
                    'validations': []
                }
                functions.append(current_function)
            elif any(keyword in line.lower() for keyword in ['tìm kiếm', 'search']):
                current_function = {
                    'name': 'Tìm kiếm',
                    'type': 'search',
                    'fields': [],
                    'validations': []
                }
                functions.append(current_function)
            elif current_function and any(keyword in line.lower() for keyword in ['bắt buộc', 'required', 'tối đa', 'maximum']):
                current_function['validations'].append(line)
        
        # If no specific functions found, create a generic one
        if not functions:
            functions.append({
                'name': 'Chức năng chính',
                'type': 'general',
                'fields': [],
                'validations': []
            })
        
        return functions
    
    def generate_ui_test_cases(self, function: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate UI test cases for a function"""
        test_cases = []
        
        # Category header
        test_cases.append({
            'ID': '',
            'Mục đích kiểm thử': 'Kiểm tra UI',
            'Các bước thực hiện': '',
            'Kết quả mong muốn': '',
            'Độ ưu tiên': ''
        })
        
        # UI test cases
        if function['type'] in ['create', 'update']:
            test_cases.append({
                'ID': f'TC_UI_{self.test_case_counter:03d}',
                'Mục đích kiểm thử': f'Kiểm tra hiển thị popup {function["name"]}',
                'Các bước thực hiện': f'1. Mở phân hệ quản trị\\n2. Nhấn nút "{function["name"]}"',
                'Kết quả mong muốn': f'Popup "{function["name"]}" hiển thị đúng với các trường và nút chức năng',
                'Độ ưu tiên': 'High'
            })
            self.test_case_counter += 1
        
        return test_cases
    
    def generate_validation_test_cases(self, function: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate validation test cases for a function"""
        test_cases = []
        
        # Category header
        test_cases.append({
            'ID': '',
            'Mục đích kiểm thử': f'Kiểm tra validate {function["name"]}',
            'Các bước thực hiện': '',
            'Kết quả mong muốn': '',
            'Độ ưu tiên': ''
        })
        
        if function['type'] in ['create', 'update']:
            # Valid data test case
            test_cases.append({
                'ID': f'TC_VAL_{self.test_case_counter:03d}',
                'Mục đích kiểm thử': f'Kiểm tra {function["name"].lower()} với thông tin hợp lệ',
                'Các bước thực hiện': f'1. Mở popup {function["name"]}\\n2. Nhập thông tin hợp lệ\\n3. Nhấn Lưu',
                'Kết quả mong muốn': 'Thao tác thành công, hiển thị thông báo thành công',
                'Độ ưu tiên': 'High'
            })
            self.test_case_counter += 1
            
            # Empty required field test case
            test_cases.append({
                'ID': f'TC_VAL_{self.test_case_counter:03d}',
                'Mục đích kiểm thử': f'Kiểm tra {function["name"].lower()} khi bỏ trống trường bắt buộc',
                'Các bước thực hiện': f'1. Mở popup {function["name"]}\\n2. Để trống trường bắt buộc\\n3. Nhấn Lưu',
                'Kết quả mong muốn': 'Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc',
                'Độ ưu tiên': 'High'
            })
            self.test_case_counter += 1
        
        return test_cases
    
    def generate_functional_test_cases(self, function: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate functional test cases for a function"""
        test_cases = []
        
        # Category header
        test_cases.append({
            'ID': '',
            'Mục đích kiểm thử': f'Kiểm tra chức năng {function["name"]}',
            'Các bước thực hiện': '',
            'Kết quả mong muốn': '',
            'Độ ưu tiên': ''
        })
        
        if function['type'] == 'search':
            test_cases.append({
                'ID': f'TC_FUN_{self.test_case_counter:03d}',
                'Mục đích kiểm thử': 'Kiểm tra tìm kiếm với từ khóa chính xác',
                'Các bước thực hiện': '1. Nhập từ khóa chính xác vào ô tìm kiếm\\n2. Nhấn Enter hoặc nút Tìm kiếm',
                'Kết quả mong muốn': 'Hiển thị danh sách kết quả khớp với từ khóa',
                'Độ ưu tiên': 'High'
            })
            self.test_case_counter += 1
        elif function['type'] == 'delete':
            test_cases.append({
                'ID': f'TC_FUN_{self.test_case_counter:03d}',
                'Mục đích kiểm thử': 'Kiểm tra xóa bản ghi hợp lệ',
                'Các bước thực hiện': '1. Chọn bản ghi cần xóa\\n2. Nhấn nút Xóa\\n3. Xác nhận xóa',
                'Kết quả mong muốn': 'Bản ghi được xóa thành công, hiển thị thông báo',
                'Độ ưu tiên': 'High'
            })
            self.test_case_counter += 1
        
        return test_cases
    
    def format_test_cases_as_csv(self, test_cases: List[Dict[str, str]]) -> str:
        """Format test cases as CSV string"""
        if not test_cases:
            return "ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên\n"
        
        csv_lines = ["ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên"]
        
        for tc in test_cases:
            # Escape commas and quotes in CSV
            def escape_csv(value):
                if not value:
                    return ""
                value = str(value).replace('"', '""')
                if ',' in value or '"' in value or '\n' in value:
                    return f'"{value}"'
                return value
            
            line = ",".join([
                escape_csv(tc.get('ID', '')),
                escape_csv(tc.get('Mục đích kiểm thử', '')),
                escape_csv(tc.get('Các bước thực hiện', '')),
                escape_csv(tc.get('Kết quả mong muốn', '')),
                escape_csv(tc.get('Độ ưu tiên', ''))
            ])
            csv_lines.append(line)
        
        return "\n".join(csv_lines)
    
    def generate_test_cases(self, content: str, use_ai: bool = False) -> List[Dict[str, str]]:
        """
        Generate test cases from content
        
        Args:
            content: Functional requirements content
            use_ai: Whether to use AI service (placeholder for future implementation)
            
        Returns:
            List of test case dictionaries
        """
        if use_ai:
            # Placeholder for AI integration
            # In a real implementation, this would call OpenAI, Anthropic, or other AI services
            prompt = self.create_professional_prompt(content)
            # For now, fall back to mock generation
            return self.generate_mock_test_cases(content)
        else:
            return self.generate_mock_test_cases(content)
