"""
AI Service Integration for Test Case Generator
Supports OpenAI ChatGPT and Google Gemini APIs
"""

import os
import json
import re
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False


class AIService:
    """AI service for generating test cases using OpenAI or Gemini"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.openai_model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        self.gemini_model = os.getenv('GEMINI_MODEL', 'gemini-pro')
        self.max_tokens = int(os.getenv('API_MAX_TOKENS', '4000'))
        self.timeout = int(os.getenv('API_TIMEOUT', '30'))
        
        # Initialize clients
        self._init_openai()
        self._init_gemini()
    
    def _init_openai(self):
        """Initialize OpenAI client"""
        if OPENAI_AVAILABLE and self.openai_api_key:
            openai.api_key = self.openai_api_key
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None
    
    def _init_gemini(self):
        """Initialize Gemini client"""
        if GEMINI_AVAILABLE and self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.gemini_client = genai.GenerativeModel(self.gemini_model)
        else:
            self.gemini_client = None
    
    def is_openai_available(self) -> bool:
        """Check if OpenAI is available and configured"""
        return OPENAI_AVAILABLE and self.openai_client is not None
    
    def is_gemini_available(self) -> bool:
        """Check if Gemini is available and configured"""
        return GEMINI_AVAILABLE and self.gemini_client is not None
    
    def get_available_models(self) -> List[str]:
        """Get list of available AI models"""
        models = []
        if self.is_openai_available():
            models.append("ChatGPT")
        if self.is_gemini_available():
            models.append("Gemini")
        return models
    
    def create_professional_prompt(self, content: str) -> str:
        """Create a professional prompt for test case generation"""
        prompt = f"""
You are an expert in software testing with extensive experience in creating professional test cases for real-world projects. Your task is to generate comprehensive test cases in Vietnamese based on the provided input requirements. The test cases must be detailed, accurate, and structured to cover UI validation, input validation, and functional testing, including positive, negative, and edge cases. Each test case should include the following fields: ID, Mục đích kiểm thử, Các bước thực hiện, Kết quả mong muốn, and Độ ưu tiên (High, Normal, Low). The output should be formatted as a CSV-like structure suitable for export to .txt and Excel files.Instructions:Analyze the input requirements to identify functional requirements, input fields, validations, and business flows.
Generate test cases in Vietnamese that are professional, detailed, and cover all aspects of the requirements (e.g., UI elements, validation rules, business logic).
Categorize test cases into groups: UI testing, input validation, and functional testing.
Ensure test cases address positive scenarios, negative scenarios, and edge cases (e.g., maximum input lengths, invalid inputs, boundary conditions).
Use the provided few-shot example to guide the format, style, and level of detail.
Format the output as a CSV-like string with headers: ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên.

Few-Shot Example:Example Input:

Chức năng cần kiểm thử (URD)
1.
1.1. Mục đích
Tài liệu này mô tả yêu cầu của người dùng đối với chức năng Quản lý chức danh, bao gồm: Tạo mới, Sửa, Xóa và Tìm kiếm chức danh trong hệ thống.
1.2. Phạm vi
Áp dụng cho phân hệ Quản trị hệ thống.
2. Yêu cầu chức năng
2.1. Tạo mới chức danh
Màn hình Popup "Tạo mới chức danh"
Trường nhập
1. Tên chức danh (bắt buộc, tối đa 64 ký tự, không trùng lặp)
2. Mô tả (không bắt buộc, tối đa 256 ký tự)
Nút chức năng - Lưu: Thêm chức danh vào hệ thống
- Hủy: Đóng popup không lưu
Luồng nghiệp vụ
1. Nhập đủ thông tin → Nhấn Lưu → Hiển thị thông báo thành công.
2. Bỏ trống trường bắt buộc → Hiển thị lỗi "Vui lòng nhập Tên chức danh".
2.2. Sửa chức danh
Thuộc tính Mô tả
Màn hình Popup "Chỉnh sửa chức danh" (pre-fill dữ liệu cũ)
Thao tác
1. Chọn chức danh từ danh sách → Nhấn nút Sửa.
2. Chỉnh sửa Tên/Mô tả → Nhấn Lưu.
Validation - Kiểm tra trùng tên chức danh (trừ chức danh đang sửa).
2.3. Xóa chức danh
Điều kiện - Không cho phép xóa nếu chức danh đang được gán cho nhân viên.
Luồng
1. Chọn chức danh → Nhấn Xóa → Hiển thị popup xác nhận.
2. Nhấn Đồng ý → Xóa thành công + thông báo.
2.4. Tìm kiếm chức danh
Giao diện: Ô nhập "Tìm kiếm" trên danh sách chức danh.
Luồng- Nhập từ khóa (tên chức danh) → Hệ thống filter real-time.
- Không phân biệt hoa thường.

Example Output:csv

ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên
,Kiểm tra UI,,,
TC_UI_001,Kiểm tra hiển thị popup Tạo mới chức danh,1. Mở phân hệ Quản trị hệ thống \n 2. Nhấn nút "Tạo mới chức danh",Popup "Tạo mới chức danh" hiển thị với các trường Tên chức danh, Mô tả và nút Lưu, Hủy,High
TC_UI_002,Kiểm tra hiển thị popup Chỉnh sửa chức danh,1. Mở phân hệ Quản trị hệ thống \n 2. Chọn một chức danh \n 3. Nhấn nút Sửa,Popup "Chỉnh sửa chức danh" hiển thị với dữ liệu pre-fill,High
TC_UI_003,Kiểm tra hiển thị popup xác nhận xóa,1. Mở phân hệ Quản trị hệ thống \n 2. Chọn một chức danh \n 3. Nhấn nút Xóa,Popup xác nhận xóa hiển thị với nút Đồng ý và Hủy,High
TC_UI_004,Kiểm tra hiển thị ô tìm kiếm,1. Mở phân hệ Quản trị hệ thống \n 2. Kiểm tra ô tìm kiếm trên danh sách chức danh,Ô tìm kiếm hiển thị với placeholder hợp lệ,High
,Kiểm tra validate trường Tạo mới chức danh,,,
TC_VAL_001,Kiểm tra tạo mới với thông tin hợp lệ,1. Mở phân hệ Quản trị hệ thống \n 2. Nhấn nút Tạo mới \n 3. Nhập Tên chức danh (ví dụ: "Quản lý") và Mô tả \n 4. Nhấn Lưu,Đăng ký chức danh thành công, hiển thị thông báo thành công,High
TC_VAL_002,Kiểm tra tạo mới khi bỏ trống Tên chức danh,1. Mở phân “

**Input**:
{file_content}

**Output**:
Generate test cases in Vietnamese based on the provided input, following the structure and style of the example output. Ensure the test cases are comprehensive, covering UI, validation, and functional aspects, with clear steps and expected results. Return the output as a CSV-like string.

more few-shot examples

test automation tools


"""
        return prompt
    
    def generate_with_openai(self, content: str) -> str:
        """Generate test cases using OpenAI ChatGPT"""
        if not self.is_openai_available():
            raise ValueError("OpenAI is not available or not configured")
        
        prompt = self.create_professional_prompt(content)
        
        try:
            response = self.openai_client.chat.completions.create(
                model=self.openai_model,
                messages=[
                    {"role": "system", "content": "Bạn là một chuyên gia kiểm thử phần mềm chuyên nghiệp tạo test case bằng tiếng Việt."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.7,
                timeout=self.timeout
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise Exception(f"OpenAI API error: {str(e)}")
    
    def generate_with_gemini(self, content: str) -> str:
        """Generate test cases using Google Gemini"""
        if not self.is_gemini_available():
            raise ValueError("Gemini is not available or not configured")
        
        prompt = self.create_professional_prompt(content)
        
        try:
            response = self.gemini_client.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.7,
                )
            )
            
            return response.text.strip()
            
        except Exception as e:
            raise Exception(f"Gemini API error: {str(e)}")
    
    def generate_test_cases(self, content: str, model: str = "ChatGPT") -> str:
        """
        Generate test cases using the specified AI model
        
        Args:
            content: Functional requirements content
            model: AI model to use ("ChatGPT" or "Gemini")
            
        Returns:
            Generated test cases as CSV string
        """
        if model == "ChatGPT":
            return self.generate_with_openai(content)
        elif model == "Gemini":
            return self.generate_with_gemini(content)
        else:
            raise ValueError(f"Unsupported model: {model}")
    
    def parse_csv_response(self, csv_content: str) -> List[Dict[str, str]]:
        """Parse CSV response from AI into list of dictionaries"""
        lines = csv_content.strip().split('\n')
        if not lines:
            return []
        
        # Find the header line (should contain the column names)
        header_line = None
        for i, line in enumerate(lines):
            if 'ID,Mục đích kiểm thử' in line or 'ID,' in line:
                header_line = i
                break
        
        if header_line is None:
            # If no proper header found, assume first line is header
            header_line = 0
        
        headers = ['ID', 'Mục đích kiểm thử', 'Các bước thực hiện', 'Kết quả mong muốn', 'Độ ưu tiên']
        test_cases = []
        
        for line in lines[header_line + 1:]:
            if not line.strip():
                continue
            
            # Simple CSV parsing (handles basic cases)
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in line:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(current_part.strip())
                    current_part = ""
                else:
                    current_part += char
            
            parts.append(current_part.strip())
            
            # Ensure we have enough parts
            while len(parts) < len(headers):
                parts.append("")
            
            # Create test case dictionary
            test_case = {}
            for i, header in enumerate(headers):
                value = parts[i] if i < len(parts) else ""
                # Clean up the value
                value = value.strip('"').strip()
                test_case[header] = value
            
            test_cases.append(test_case)
        
        return test_cases
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of AI services"""
        return {
            'openai': {
                'available': self.is_openai_available(),
                'model': self.openai_model if self.is_openai_available() else None,
                'configured': bool(self.openai_api_key)
            },
            'gemini': {
                'available': self.is_gemini_available(),
                'model': self.gemini_model if self.is_gemini_available() else None,
                'configured': bool(self.gemini_api_key)
            }
        }
