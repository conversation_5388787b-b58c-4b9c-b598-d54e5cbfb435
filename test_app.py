#!/usr/bin/env python3
"""
Test script for the Test Case Generator application
"""

import os
import sys
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from project_manager import ProjectManager
from file_parser import FileParser
from test_case_generator import <PERSON>CaseGenerator
from output_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_project_manager():
    """Test project management functionality"""
    print("Testing Project Manager...")
    
    # Use temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        pm = ProjectManager(temp_dir)
        
        # Test project creation
        project_id = pm.create_project("Test Project", "A test project")
        print(f"✅ Created project with ID: {project_id}")
        
        # Test project retrieval
        project = pm.get_project(project_id)
        assert project is not None
        assert project['name'] == "Test Project"
        print("✅ Project retrieval works")
        
        # Test project listing
        projects = pm.list_projects()
        assert len(projects) == 1
        print("✅ Project listing works")
        
    print("✅ Project Manager tests passed!\n")


def test_file_parser():
    """Test file parsing functionality"""
    print("Testing File Parser...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        fp = FileParser(temp_dir)
        
        # Test supported file check
        assert fp.is_supported_file("test.txt")
        assert fp.is_supported_file("test.docx")
        assert fp.is_supported_file("test.xlsx")
        assert not fp.is_supported_file("test.pdf")
        print("✅ File type validation works")
        
        # Create a test text file
        test_file = os.path.join(temp_dir, "test_input.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("Test content for parsing\nLine 2\nLine 3")
        
        # Test text file parsing
        content = fp.parse_txt_file(test_file)
        assert "Test content for parsing" in content
        print("✅ Text file parsing works")
        
    print("✅ File Parser tests passed!\n")


def test_test_case_generator():
    """Test test case generation functionality"""
    print("Testing Test Case Generator...")
    
    tcg = TestCaseGenerator()
    
    # Test with sample Vietnamese content
    sample_content = """
    Chức năng cần kiểm thử (URD)
    1.1. Mục đích: Quản lý chức danh
    2.1. Tạo mới chức danh
    - Tên chức danh (bắt buộc, tối đa 64 ký tự)
    - Mô tả (không bắt buộc, tối đa 256 ký tự)
    - Nút Lưu: Thêm chức danh vào hệ thống
    """
    
    # Generate test cases
    test_cases = tcg.generate_test_cases(sample_content)
    assert len(test_cases) > 0
    print(f"✅ Generated {len(test_cases)} test cases")
    
    # Check test case structure
    for tc in test_cases:
        assert isinstance(tc, dict)
        assert 'ID' in tc
        assert 'Mục đích kiểm thử' in tc
        print("✅ Test case structure is correct")
        break
    
    print("✅ Test Case Generator tests passed!\n")


def test_output_handler():
    """Test output handling functionality"""
    print("Testing Output Handler...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        oh = OutputHandler(temp_dir)
        
        # Sample test cases
        sample_test_cases = [
            {
                'ID': '',
                'Mục đích kiểm thử': 'Kiểm tra UI',
                'Các bước thực hiện': '',
                'Kết quả mong muốn': '',
                'Độ ưu tiên': ''
            },
            {
                'ID': 'TC_UI_001',
                'Mục đích kiểm thử': 'Kiểm tra hiển thị popup',
                'Các bước thực hiện': '1. Mở ứng dụng\\n2. Nhấn nút tạo mới',
                'Kết quả mong muốn': 'Popup hiển thị đúng',
                'Độ ưu tiên': 'High'
            }
        ]
        
        # Test TXT file generation
        txt_path = oh.save_txt_file(sample_test_cases, "Test Project")
        assert os.path.exists(txt_path)
        print("✅ TXT file generation works")
        
        # Test Excel file generation
        excel_path = oh.save_excel_file(sample_test_cases, "Test Project")
        assert os.path.exists(excel_path)
        print("✅ Excel file generation works")
        
        # Test display formatting
        display_content = oh.format_test_cases_for_display(sample_test_cases)
        assert "TC_UI_001" in display_content
        print("✅ Display formatting works")
        
    print("✅ Output Handler tests passed!\n")


def test_integration():
    """Test integration of all components"""
    print("Testing Integration...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Initialize all components
        pm = ProjectManager(os.path.join(temp_dir, "data"))
        fp = FileParser(os.path.join(temp_dir, "uploads"))
        tcg = TestCaseGenerator()
        oh = OutputHandler(os.path.join(temp_dir, "outputs"))
        
        # Create project
        project_id = pm.create_project("Integration Test", "Testing integration")
        
        # Create test file
        test_file = os.path.join(temp_dir, "test_requirements.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("""
            Chức năng Quản lý người dùng
            1. Tạo mới người dùng
            - Tên đăng nhập (bắt buộc, tối đa 50 ký tự)
            - Email (bắt buộc, định dạng email)
            - Mật khẩu (bắt buộc, tối thiểu 8 ký tự)
            2. Sửa thông tin người dùng
            3. Xóa người dùng
            """)
        
        # Simulate file upload
        class MockFile:
            def __init__(self, path):
                self.name = path
        
        mock_file = MockFile(test_file)
        saved_path, file_type = fp.save_uploaded_file(mock_file, "test_requirements.txt")
        
        # Add file to project
        pm.add_file_to_project(project_id, saved_path, "test_requirements.txt", file_type)
        
        # Parse file content
        content = fp.parse_file(saved_path, file_type)
        
        # Generate test cases
        test_cases = tcg.generate_test_cases(content)
        
        # Generate output files
        txt_path, excel_path = oh.generate_output_files(test_cases, "Integration Test")
        
        # Add outputs to project
        pm.add_generated_output(project_id, txt_path, excel_path)
        
        # Verify everything worked
        project = pm.get_project(project_id)
        assert len(project['files']) == 1
        assert len(project['generated_outputs']) == 1
        assert os.path.exists(txt_path)
        assert os.path.exists(excel_path)
        
        print("✅ Integration test passed!")
        print(f"   - Project created: {project['name']}")
        print(f"   - Files uploaded: {len(project['files'])}")
        print(f"   - Test cases generated: {len(test_cases)}")
        print(f"   - Output files created: TXT and Excel")
    
    print("✅ All integration tests passed!\n")


def main():
    """Run all tests"""
    print("🧪 Running Test Case Generator Tests\n")
    print("=" * 50)
    
    try:
        test_project_manager()
        test_file_parser()
        test_test_case_generator()
        test_output_handler()
        test_integration()
        
        print("=" * 50)
        print("🎉 ALL TESTS PASSED! 🎉")
        print("The Test Case Generator application is ready to use.")
        print("\nTo run the application:")
        print("  python app.py")
        print("\nThen open your browser to: http://localhost:7860")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
